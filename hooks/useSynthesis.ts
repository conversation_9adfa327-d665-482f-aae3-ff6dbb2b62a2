// hooks/useSynthesis.ts
import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { IAppState } from '@/constant/type';
import { createSynthesis, getSynthesisList } from '@/server';
import toast from 'react-hot-toast';
import { createParams, getLocalSession, rsaEncrypt } from '@/utils';
import { ItemConfig } from '@/world/Config/ItemConfig';
import { getCdnLink } from '@/AvatarOrdinalsBrowser/utils';
import { setUserBasicInfo } from '@/store/app';
import useBagInventory from '@/hooks/useBagInventory';

// 定义API返回的数据结构类型
export interface ISynthesisItem {
  synthesisTag: string; // 合成配方ID
  itemTag: string; // 产出物品tag
  itemName?: string; // 产出物品名称（可能需要从其他地方获取）
  itemIcon?: string; // 产出物品图标（可能需要从其他地方获取）
  maxDurability: number;
  quality: number;
  synthesis: {
    tag: string; // 材料tag
    type: 'equipment' | 'resource'; // 材料类型
    count: number; // 合成所需材料数量
    currentCount: number; // 当前拥有的数量
    name?: string; // 材料名称（可能需要从其他地方获取）
    icon?: string; // 材料图标（可能需要从其他地方获取）
  }[];
}

// 筛选类型
export type FilterType = 'all' | 'available' | 'unavailable' | string;

// 扩展的合成项，包含计算结果
export interface IExtendedSynthesisItem extends ISynthesisItem {
  canSynthesize: number; // 最大可合成数量
}

export type RecipesWithStatusItemType = Omit<ISynthesisItem, 'synthesis'> & {
  active: boolean;
};

export default function useSynthesis() {
  const { btcAddress, userBasicInfo } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const dispatch = useDispatch();
  const { addNewItem } = useBagInventory();

  // 原始合成列表数据
  const [synthesisData, setSynthesisData] = useState<ISynthesisItem[]>([]);

  const [loading, setLoading] = useState(false);
  const [synthesizing, setSynthesizing] = useState(false);
  const [filter, setFilter] = useState<FilterType>('all');
  const [selectedRecipe, setSelectedRecipe] = useState<IExtendedSynthesisItem | null>(null);

  // 处理后的合成列表数据
  const [processedRecipes, setProcessedRecipes] = useState<{
    // 带有可合成数量的完整配方列表
    extendedRecipes: IExtendedSynthesisItem[];
    // 带有可合成状态的简化配方列表（用于UI展示）
    recipesWithStatus: RecipesWithStatusItemType[];
    // recipesWithStatus: IRecipesWithStatus[];
    // 当前过滤后的配方列表
    filteredRecipes: {
      synthesisTag: string;
      itemTag: string;
      itemIcon?: string;
      itemName?: string;
      active: boolean;
    }[];
  }>({
    extendedRecipes: [],
    recipesWithStatus: [],
    filteredRecipes: [],
  });

  // 获取装备配置数据的辅助函数
  const getItemConfig = (tag: string | number): Promise<any> => {
    return new Promise((resolve) => {
      ItemConfig.getInstance().getData(Number(tag), (data) => {
        resolve(data || null);
      });
    });
  };

  // 获取合成配方列表
  const fetchSynthesisList = useCallback(async () => {
    if (!btcAddress) return;

    setLoading(true);
    try {
      const res = await getSynthesisList();
      if (res.data?.data) {
        const rawSynthesisData = res.data.data || [];
        const processedData = await Promise.all(
          rawSynthesisData.map(async (item: ISynthesisItem) => {
            // 获取产出物品配置
            const itemConfig = await getItemConfig(item.itemTag);
            // 获取所有材料配置
            const materials = await Promise.all(
              item.synthesis.map(async (material) => {
                const materialConfig = await getItemConfig(material.tag);
                return {
                  ...material,
                  name: materialConfig?.name || '',
                  icon: materialConfig?.icon_url ? getCdnLink(materialConfig.icon_url) : '',
                };
              })
            );

            // 返回完整的合成配方数据
            return {
              ...item,
              itemName: itemConfig?.name || '',
              itemIcon: itemConfig?.icon_url ? getCdnLink(itemConfig.icon_url) : '',
              synthesis: materials,
              quality: itemConfig?.quality,
            };
          })
        );
        setSynthesisData(processedData);
        return processedData;
      }
    } catch (error) {
      console.error('获取合成配方列表失败:', error);
    } finally {
      setLoading(false);
    }
  }, [btcAddress]);

  // 执行合成操作
  const synthesize = async (synthesisTag: string, token?: string, capCallback?: () => void) => {
    if (!btcAddress || !synthesisTag) return false;

    setSynthesizing(true);
    // const loadingToast = toast.loading("合成中...");

    try {
      const params = createParams(btcAddress, '/synthesis/create/' + synthesisTag);
      const encrypted = rsaEncrypt(params);
      const headers = {
        sw: encrypted,
        address: btcAddress,
        session: getLocalSession(btcAddress).sessionId,
        // 'x-captcha-token': token ? token : undefined,
      };
      const res = await createSynthesis({ synthesisTag }, headers);
      if (res.data.code === 1) {
        setTimeout(() => {
          toast.success('Crafting Successful!');
        }, 4000);
        const { workbenchTotalCount, workbenchCurrentCount, userItemInfo } = res.data.data;
        if (userBasicInfo) {
          dispatch(
            setUserBasicInfo({
              ...userBasicInfo,
              workbenchConfig: {
                workbenchTotalCount,
                workbenchCurrentCount,
              },
            })
          );

          //增加延迟 让npc先结束打造动作
          setTimeout(() => {
            //更新背包数据
            if (userItemInfo) {
              addNewItem(userItemInfo);
            }
          }, 4000);
        }
        return true;
      } else if (res.data.code === 125) {
        capCallback?.();
        return false;
      } else {
        toast.error(res.data.msg || res.data.message[0], {
          duration: 6000,
        });
        return false;
      }
    } catch (error: any) {
      toast.error(error.message);
      return false;
    } finally {
      // toast.dismiss(loadingToast);
      setSynthesizing(false);
    }
  };

  // 计算配方的可合成数量
  const calculateSynthesizableCount = useCallback((recipe: ISynthesisItem): number => {
    if (!recipe.synthesis || recipe.synthesis.length === 0) {
      return 0;
    }

    // 计算可合成的最大数量
    return Math.min(
      ...recipe.synthesis.map((material) => Math.floor(material.currentCount / material.count))
    );
  }, []);

  // 扩展配方数据，添加可合成数量
  const extendRecipes = useCallback(
    (recipes: ISynthesisItem[]): IExtendedSynthesisItem[] => {
      return recipes.map((recipe) => ({
        ...recipe,
        canSynthesize: calculateSynthesizableCount(recipe),
      }));
    },
    [calculateSynthesizableCount]
  );

  // 生成带有可合成状态的配方列表
  const generateRecipeListWithStatus = useCallback((recipes: IExtendedSynthesisItem[]) => {
    // 生成带状态的简化列表
    return recipes.map((recipe) => ({
      synthesisTag: recipe.synthesisTag,
      itemTag: recipe.itemTag,
      itemIcon: recipe.itemIcon,
      itemName: recipe.itemName,
      quality: recipe.quality,
      maxDurability: recipe.maxDurability,
      active: recipe.canSynthesize > 0, // 如果至少可以合成一个，则为激活状态
    }));
  }, []);

  // 筛选配方
  const filterRecipes = useCallback(
    (
      recipes: {
        synthesisTag: string;
        itemTag: string;
        itemIcon?: string;
        itemName?: string;
        active: boolean;
      }[],
      filterType: FilterType
    ) => {
      switch (filterType) {
        case 'available':
          return recipes.filter((r) => r.active);
        case 'unavailable':
          return recipes.filter((r) => !r.active);
        default:
          return recipes;
      }
    },
    []
  );

  // 当合成列表更新时，重新处理数据
  useEffect(() => {
    if (synthesisData.length) {
      // 扩展配方数据，添加可合成数量
      const extendedRecipes = extendRecipes(synthesisData);

      // 生成带状态的列表
      const recipesWithStatus = generateRecipeListWithStatus(extendedRecipes);

      // 应用筛选
      const filteredRecipes = filterRecipes(recipesWithStatus, filter);

      setProcessedRecipes({
        extendedRecipes,
        recipesWithStatus,
        filteredRecipes,
      });

      // 如果有选中的配方，更新它的数据
      if (selectedRecipe) {
        const updatedRecipe = extendedRecipes.find(
          (r) => r.synthesisTag === selectedRecipe.synthesisTag
        );
        if (updatedRecipe) {
          setSelectedRecipe(updatedRecipe);
        }
      } else if (extendedRecipes.length > 0) {
        // 如果没有选中的配方，选择第一个
        setSelectedRecipe(extendedRecipes[0]);
      }
    }
  }, [synthesisData, filter, extendRecipes, generateRecipeListWithStatus, filterRecipes]);

  // 修改筛选类型
  const changeFilter = useCallback((newFilter: FilterType) => {
    setFilter(newFilter);
  }, []);

  // 检查配方是否可以合成
  const canSynthesize = useCallback((recipe: ISynthesisItem): boolean => {
    return recipe.synthesis.every((material) => material.currentCount >= material.count);
  }, []);

  // 根据合成tag获取配方
  const getRecipeByTag = useCallback(
    (synthesisTag: string): IExtendedSynthesisItem | undefined => {
      return processedRecipes.extendedRecipes.find(
        (recipe) => recipe.synthesisTag === synthesisTag
      );
    },
    [processedRecipes.extendedRecipes]
  );

  // 关闭弹窗要重置数据
  const reset = () => {
    setSelectedRecipe(null);
    setSynthesisData([]);
    setSynthesizing(false);
    setProcessedRecipes({
      extendedRecipes: [],
      recipesWithStatus: [],
      filteredRecipes: [],
    });
  };

  return {
    loading,
    synthesizing,
    synthesisData,
    recipes: processedRecipes.extendedRecipes,
    recipesWithStatus: processedRecipes.recipesWithStatus,
    filteredRecipes: processedRecipes.filteredRecipes,
    selectedRecipe,
    filter,

    // 方法
    setSelectedRecipe,
    fetchSynthesisList,
    synthesize,
    changeFilter,
    canSynthesize,
    getRecipeByTag,
    setSynthesisData,
    setSynthesizing,
    reset,

    // 辅助功能
    calculateSynthesizableCount,
    extendRecipes,
    generateRecipeListWithStatus,
    filterRecipes,
  };
}
