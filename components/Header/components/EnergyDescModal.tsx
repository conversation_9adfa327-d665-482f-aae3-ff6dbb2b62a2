import { px2rem } from '@/utils/px2rem';
import BasicRulesContent from '@/components/EventRules/components/BasicRulesContent';
import Dialog from '@/commons/Dialog';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';


// TODO: 替换为体力的介绍
const popupImages = [
  {
    src: '/image/easterEgg/orderTree_step1.png',
    imgObj: '',
    alt: 'Popup image 1',
    width: px2rem(252),
    height: px2rem(252),
    className: '',
    description: 'Craft axes from the NPC to start chopping trees.',
  },
  {
    src: '/image/easterEgg/orderTree_step2.png',
    imgObj: '',
    alt: 'Popup image 2',
    width: px2rem(252),
    height: px2rem(252),
    className: 'tep2',
    description: 'Find glowing trees and chop them in the right order.',
  },
  {
    src: '/image/easterEgg/easterRule_step3.png',
    imgObj: 'tep3',
    alt: 'Popup image 3',
    width: px2rem(252),
    height: px2rem(252),
    className: '',
    description: 'Complete the challenge to earn token rewards!',
  },
];

const event1 = {
  src: '/image/easterEgg/orderTree_title.png',

  alt: 'event2',
  width: px2rem(400),
  height: px2rem(100),
  className: '',
};

interface IEnergyDescModalProps {
  modal: {
    isOpen: boolean;
    open: () => void;
    close: () => void;
    onClose: () => void;
  };
}

const EnergyDescModal: React.FC<IEnergyDescModalProps> = ({ modal }) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  if (!isMounted) {
    return null;
  }
  return (
    <Dialog isOpen={modal.isOpen} onClose={modal.onClose}>
      <BasicRulesContent
        onClose={modal.onClose}
        eventTitle={event1}
        popupImages={popupImages}
        bgImgSrc={'/image/easterEgg/orderTree_bg.png'}
        mainTitle="Hidden Challenge"
        subTitle="Chop Master"
      />
    </Dialog>
  );
};

export default memo(EnergyDescModal);

export function useEnergyDescModal(onClose: () => void = () => false) {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const open = useCallback(() => {
    setIsOpen(true);
  }, []);
  const close = useCallback(() => {
    setIsOpen(false);
    onClose();
  }, []);
  const handleClose = useCallback(() => {
    setIsOpen(false);
    onClose();
  }, [onClose]);

  const modal = useMemo(() => {
    return {
      isOpen,
      open,
      close,
      onClose: handleClose,
    };
  }, [close, handleClose, isOpen, open]);

  return modal;
}
