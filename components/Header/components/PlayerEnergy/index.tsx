import { usePlayerEnergySelector } from '@/contexts/playerEnergyContext';

import { AnimatePresence, color, motion, useAnimate } from 'motion/react';

import {
  CSSProperties,
  forwardRef,
  HTMLAttributes,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import styled from 'styled-components';
import EnergyDescModal, { useEnergyDescModal } from '../EnergyDescModal';

const StyledPlayerEnergy = styled(motion.div)<{ $percentage: number; $bgColor: string }>`
  position: relative;
  width: var(--percentage);
  flex-shrink: 0;
  border-radius: 0.25rem;
  border: solid 0.0625rem #140f08;
  border-color: ${(props) => (props.$percentage <= 0 ? 'transparent' : '#140f08')};
  background: ${(props) => (props.$percentage <= 0 ? 'transparent' : props.$bgColor)};
  border-top-right-radius: 0.25rem;
  margin-left: -0.09375rem;
  border-bottom-right-radius: 0.25rem;
  max-width: 100%;
  height: calc(0.75rem - 2 * 0.0625rem);
  box-sizing: content-box;
  transition: all 0.3s ease-out;

  /* position: fixed;
  top: 7.25rem;
  left: 4rem; */
`;
const StyledPlayerEnergyWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  width: 100%;
  position: relative;
  gap: 0.125rem;
`;

const StyledPlayerEnergyContainer = styled(motion.div)`
  height: calc(0.75rem - 2 * 0.0625rem);
  box-sizing: content-box;
  flex: 1;
  border-radius: 0.25rem;
  border: solid 0.0625rem #140f08;
  background: rgba(253, 240, 210, 0.5);
  /* overflow: hidden;   */
  flex-shrink: 0;
  display: flex;
  align-items: center;
  position: relative;
`;

const EnergyInfo = styled(motion.div)`
  position: fixed;
  right: 0;
  position: absolute;
  top: 100%;
  /* transform: translateY(-0.25rem); */

  box-sizing: border-box;
  height: 1.875rem;
  padding: 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.5rem;
  border: solid 0.0625rem #542d00;
  background: #fff;
  box-shadow: 0px 0.125rem 0.25rem 0px rgba(0, 0, 0, 0.5);

  color: #a58061;
  font-family: 'JetBrains Mono';
  font-size: 0.625rem;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -0.04em;
  z-index: 11;
  & > span:first-of-type {
    color: #ff8316;
  }
`;
const EnergyChange = styled(motion.div)<{ $diff: number }>`
  position: fixed;
  left: 100%;
  position: absolute;
  bottom: 0;
  /* transform: translateY(-0.25rem); */

  box-sizing: border-box;
  height: 1.5rem;
  padding: 0.375rem 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.5rem;
  border: solid 0.0625rem #542d00;
  box-shadow: 0px 0.125rem 0.25rem 0px rgba(0, 0, 0, 0.5);
  background: ${(props) => (props.$diff <= 0 ? '#FFE100' : '#140F08')};
  color: ${(props) => (props.$diff <= 0 ? '#140F08' : '#FFF')};

  font-size: 0.625rem;
  font-weight: 700;
  line-height: normal;
  letter-spacing: -0.025rem;
  z-index: 11;
`;

const StyledSvgWrapper = styled.span`
  display: flex;
  width: 0.875rem;
  height: 0.875rem;
  justify-content: center;
  align-items: center;
  & > svg {
    width: 0.875rem;
    height: 0.875rem;
  }
`;

const EnergySvg = ({ style = {} }: { style?: CSSProperties }) => {
  return (
    <StyledSvgWrapper style={{ ...style }}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="8"
        height="12"
        viewBox="0 0 8 12"
        fill="none"
        style={{ ...style }}>
        <path
          d="M5.33627 1.24428C5.36834 1.12769 5.37302 1.00126 5.33454 0.884569C5.29614 0.767926 5.21912 0.671346 5.11627 0.613759C5.01342 0.556171 4.89083 0.540978 4.77131 0.569203C4.65172 0.59738 4.54637 0.667438 4.46373 0.755721C4.30555 0.92554 4.14936 1.09647 3.99514 1.26851C3.02929 2.346 2.14124 3.46705 1.33098 4.63167L0.55771 5.74339L1.97401 6.51848C2.9323 7.04306 3.91134 7.51903 4.91113 7.94637L4.25772 5.72613C4.25465 5.73065 4.25159 5.73517 4.24853 5.73969C3.37795 7.02619 2.71788 8.45514 2.26834 10.0266C2.20309 10.2546 2.14228 10.4857 2.0859 10.7198C2.05882 10.8343 2.05043 10.9561 2.08042 11.0704C2.11036 11.1846 2.17535 11.2814 2.26741 11.3437C2.35947 11.406 2.47347 11.4304 2.59065 11.4157C2.70787 11.401 2.81781 11.348 2.9141 11.2802C3.11043 11.1409 3.30232 10.9985 3.48979 10.8531C4.78141 9.85156 5.86252 8.70752 6.73311 7.42103C6.73617 7.41651 6.73923 7.41198 6.74228 7.40746L7.76125 5.90012L6.08887 5.18722C5.08866 4.76086 4.0677 4.38312 3.02599 4.054L3.66902 5.94081C4.23847 4.64136 4.73012 3.29835 5.14398 1.91178C5.21006 1.69039 5.27415 1.46789 5.33627 1.24428Z"
          fill="#FF8316"
        />
      </svg>
    </StyledSvgWrapper>
  );
};
const EnergyStokeSvg: React.FC<HTMLAttributes<HTMLSpanElement>> = ({ style, ...restProps }) => {
  return (
    <StyledSvgWrapper {...restProps} style={{ ...style }}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="12"
        height="16"
        viewBox="0 0 12 16"
        fill="none">
        <path
          d="M6.58301 1.31836C6.83224 1.2595 7.10237 1.28772 7.33691 1.41895V1.41992C7.57159 1.55144 7.73643 1.76596 7.81641 2.00879L7.84277 2.10059C7.89362 2.31603 7.87584 2.52841 7.82617 2.70898L7.8252 2.70996C7.75337 2.96853 7.67993 3.22641 7.60352 3.48242C7.35142 4.327 7.07073 5.15621 6.76855 5.97266C7.31684 6.1768 7.8613 6.39227 8.40039 6.62207L10.3115 7.4375L10.8691 7.67578L10.5293 8.17773L9.36523 9.90039C9.35919 9.90933 9.35419 9.91701 9.35059 9.92188C9.3476 9.9259 9.34472 9.92939 9.34375 9.93066C8.31952 11.4405 7.05051 12.7818 5.54004 13.9531C5.32082 14.1231 5.09569 14.2905 4.86523 14.4541L4.86426 14.4551C4.71102 14.5629 4.50743 14.6653 4.26758 14.6953L4.26855 14.6963C4.02806 14.7264 3.77303 14.6786 3.55664 14.5322C3.3407 14.386 3.20125 14.1676 3.13965 13.9326C3.07833 13.6986 3.09968 13.4716 3.14258 13.29V13.2881C3.20875 13.0134 3.28113 12.7423 3.35742 12.4756C3.64037 11.4866 3.99442 10.5435 4.4209 9.64746C4.03221 9.45179 3.64418 9.25189 3.26074 9.04199L1.64258 8.15625L1.1543 7.88965L1.47266 7.43262L2.35645 6.16211C3.29438 4.814 4.32225 3.51588 5.43945 2.26953C5.61746 2.07096 5.79756 1.87313 5.98047 1.67676L5.98145 1.67578C6.12809 1.51913 6.33141 1.37781 6.58301 1.31836Z"
          fill="#F8B81D"
          stroke="#140F08"
          strokeLinecap="round"
        />
      </svg>
      <span></span>
    </StyledSvgWrapper>
  );
};

const colorArr = [
  {
    color: '#41D541',
    limit: 80,
  },
  {
    color: '#FFCC00',
    limit: 50,
  },
  {
    color: '#FF8316',
    limit: 20,
  },
  {
    color: '#FF2720',
    limit: 0,
  },
];

const PlayerEnergy = forwardRef((_props: any, _ref) => {
  const { energy, totalEnergy } = usePlayerEnergySelector((state) => state);
  const [showEnergyInfo, setShowEnergyInfo] = useState(false);

  const [diff, setDiff] = useState(0);
  const [energyChangeRef, energyChangeAnimate] = useAnimate();

  const [energyState, setEnergyState] = useState<number>(0);
  const energyChangeAnimateRef = useRef<any>(null);

  const handleAnimation = () => {
    if (diff !== 0) {
      if (energyChangeAnimateRef.current) {
        energyChangeAnimateRef.current.stop();
      }
      energyChangeAnimateRef.current = energyChangeAnimate(
        energyChangeRef.current,
        {
          opacity: 1,
          scale: 1,
          transform: 'translateY(-0.25rem) translateX(-1rem)',
        },
        { duration: 1.5, ease: 'circIn' }
      );
      energyChangeAnimateRef.current.then(() => {
        setDiff(0);
      });
    }
  };

  useEffect(() => {
    handleAnimation();
  }, [diff]);

  useEffect(() => {
    setEnergyState((prev) => {
      const diff = energy - prev;
      setDiff(diff);

      return energy;
    });
  }, [energy]);

  const percentage = useMemo(() => {
    return (energyState / totalEnergy) * 100;
  }, [energyState, totalEnergy]);

  const currentColor = useMemo(() => {
    let color = '#41D541';
    for (let index = 0; index < colorArr.length; index++) {
      const item = colorArr[index];
      if (percentage >= item.limit) {
        color = item.color;
        break;
      }
    }
    return color;
  }, [percentage]);

  return (
    <StyledPlayerEnergyWrapper>
      <EnergyStokeSvg
        style={{
          width: '1rem',
          height: '1rem',
          cursor: 'pointer',
        }}
      />

      <StyledPlayerEnergyContainer
        animate={{
          '--percentage': `${percentage}%`,
        }}
        onMouseEnter={() => setShowEnergyInfo(true)}
        onMouseLeave={() => setShowEnergyInfo(false)}>
        <StyledPlayerEnergy $percentage={percentage} $bgColor={currentColor}>
          <AnimatePresence initial={true}>
            {diff !== 0 && (
              <EnergyChange
                ref={energyChangeRef}
                $diff={diff}
                initial={{ opacity: 0, scale: 0, transform: 'translateY(0rem) translateX(-1rem)' }}
                exit={{ opacity: 0, scale: 0, transform: 'translateY(0rem) translateX(-1rem)' }}>
                <EnergySvg />
                <span>{diff <= 0 ? diff : `+${diff}`}</span>
              </EnergyChange>
            )}
          </AnimatePresence>
        </StyledPlayerEnergy>
        <AnimatePresence initial={true}>
          {showEnergyInfo && (
            <EnergyInfo
              initial={{ opacity: 0, scale: 0, transform: 'translateY(0rem)' }}
              animate={{ opacity: 1, scale: 1, transform: 'translateY(-0.25rem)' }}
              exit={{ opacity: 0, scale: 1, transform: 'translateY(-0.5rem)' }}>
              <span>{energyState}</span>
              <span>&nbsp;/&nbsp;</span>
              <span>{totalEnergy}</span>
            </EnergyInfo>
          )}
        </AnimatePresence>
      </StyledPlayerEnergyContainer>

      {/* bellow component just for develop testing */}
      {/* <div className="btnBox">
        <button
          onClick={() => {
            setCurrentStamin((prev) => prev - 10);
          }}>
          -
        </button>
        <button
          onClick={() => {
            setCurrentStamin((prev) => prev + 10);
          }}>
          +
        </button>
      </div> */}
    </StyledPlayerEnergyWrapper>
  );
});

PlayerEnergy.displayName = 'PlayerEnergy';

export default PlayerEnergy;
